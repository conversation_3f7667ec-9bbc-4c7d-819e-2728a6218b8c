import { Api<PERSON><PERSON> } from '../entities/api-key.entity';

/**
 * API Key data for response (excludes sensitive information)
 */
export interface ApiKeyResponseData {
  id: string;
  keyId: string;
  clientId: string;
  status: string;
  expiresAt: Date | null;
  lastUsedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Pagination metadata
 */
export interface PaginationMetadata {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Response for getting API keys
 */
export interface GetApiKeysResponse {
  success: boolean;
  message: string;
  data: ApiKeyResponseData[];
  pagination: PaginationMetadata;
}

/**
 * Convert ApiKey entity to response data (excludes sensitive information)
 */
export function toApiKeyResponseData(apiKey: ApiKey): ApiKeyResponseData {
  return {
    id: apiKey.id,
    keyId: apiKey.keyId,
    clientId: apiKey.clientId,
    status: apiKey.status,
    expiresAt: apiKey.expiresAt,
    lastUsedAt: apiKey.lastUsedAt,
    createdAt: apiKey.createdAt,
    updatedAt: apiKey.updatedAt,
  };
} 