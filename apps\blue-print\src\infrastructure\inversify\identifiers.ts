export const AppTypes = {
  Application: Symbol.for('Application'),
  BluePrintJobService: Symbol.for('BluePrintJobService'),
  GrpcServerProvider: Symbol.for('GrpcServerProvider'),
  GrpcServerConfig: Symbol.for('GrpcServerConfig'),

  // Connect-RPC symbols (following existing patterns)
  ConnectServer: Symbol.for('ConnectServer'),
  ConnectServerProvider: Symbol.for('ConnectServerProvider'),
  ConnectAgentService: Symbol.for('ConnectAgentService'),

  // Command Management
  CommandManager: Symbol.for('CommandManager'),
  CommandChannelManager: Symbol.for('CommandChannelManager'),
};
export const ControllerTypes = {
  AgentsController: Symbol.for('AgentsController'),
  DevicesController: Symbol.for('DevicesController'),
  MonitoringPointsController: Symbol.for('MonitoringPointsController'),
  MonitoringSourcesController: Symbol.for('MonitoringSourcesController'),
  HealthController: Symbol.for('HealthController'),
  UnitController: Symbol.for('UnitController'),
  SystemController: Symbol.for('SystemController'),
  ProtocolsController: Symbol.for('ProtocolsController'),
  ApiKeysController: Symbol.for('ApiKeysController'),
};
