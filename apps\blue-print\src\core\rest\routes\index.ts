import express, { Router } from 'express';
import * as agentsRouter from './infrastructure/agents.routes';
import * as devicesRouter from './infrastructure/devices.routes';
import * as monitoringPointRouter from './infrastructure/monitoring-point.routes';
import * as monitoringSourcesRouter from './infrastructure/monitoring-sources.routes';

import * as healthRouter from './common/health.routes';
import * as sseRouter from './common/sse-routes';
import * as apiKeysRouter from './common/api-keys.routes';
import * as systemRouter from './system/system.routes';
import * as protocolsRouter from './system/protocols.routes';

import { logger } from '../../../infrastructure/logger';

export function v1Routes(): Router {
  logger.debug('Setting v1 routes', 'Routes.V1');

  const router = express.Router();

  router.use('/agents', agentsRouter.v1());
  router.use('/devices', devicesRouter.v1());
  router.use('/health', healthRouter.v1());
  router.use('/protocols', protocolsRouter.v1());
  router.use('/sse', sseRouter.v1());
  router.use('/api-keys', apiKeysRouter.v1());
  router.use('/system', systemRouter.v1());
  router.use('/monitoring-point', monitoringPointRouter.v1());
  router.use('/monitoring-sources', monitoringSourcesRouter.v1());

  return router;
}
