{"name": "shared-utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/shared-utils/src", "projectType": "library", "tags": ["shared utils"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/shared-utils", "main": "modules/shared-utils/src/index.ts", "tsConfig": "modules/shared-utils/tsconfig.lib.json", "assets": ["modules/shared-utils/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["modules/shared-utils/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "modules/shared-utils/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "coverage": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/modules/shared-utils"], "options": {"jestConfig": "modules/shared-utils/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "coverageReporters": ["text", "html", "lcov", "json"], "skipNxCache": true}}}}