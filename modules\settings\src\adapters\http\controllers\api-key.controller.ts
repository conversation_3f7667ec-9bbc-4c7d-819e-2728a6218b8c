import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { GenerateApiKeyUseCase } from '../../../core/usecases/api-key/generate-api-key.usecase';
import { GetApiKeysUseCase } from '../../../core/usecases/api-key/get-api-keys.usecase';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { ApiKeyGenerationResponse } from '../../../core/dtos/generate-api-key.dto';
import { GetApiKeysResponse, toApiKeyResponseData } from '../../../core/dtos/get-api-keys.dto';

/**
 * HTTP Controller for API Key management endpoints
 */
@injectable()
export class ApiKeyController {
  constructor(
    @inject(InfrastructureIdentifier.GenerateApiKeyUseCase)
    private readonly generateApiKeyUseCase: GenerateApiKeyUseCase,
    @inject(InfrastructureIdentifier.GetApiKeysUseCase)
    private readonly getApiKeysUseCase: GetApiKeysUseCase
  ) {}

  /**
   * Generate a new API key
   * POST /api/v1/api-keys
   */
  async generateApiKey(req: Request, res: Response): Promise<void> {
    try {
      const { clientId, expiresAt } = req.body;

      // Validate required fields
      if (!clientId || typeof clientId !== 'string') {
        res.status(400).json({
          error: 'Bad Request',
          message: 'clientId is required and must be a string',
          code: 'INVALID_CLIENT_ID'
        });
        return;
      }

      // Parse expiration date if provided
      let parsedExpiresAt: Date | undefined;
      if (expiresAt) {
        parsedExpiresAt = new Date(expiresAt);
        if (isNaN(parsedExpiresAt.getTime())) {
          res.status(400).json({
            error: 'Bad Request',
            message: 'expiresAt must be a valid ISO date string',
            code: 'INVALID_EXPIRATION_DATE'
          });
          return;
        }

        // Check if expiration date is in the future
        if (parsedExpiresAt <= new Date()) {
          res.status(400).json({
            error: 'Bad Request',
            message: 'expiresAt must be a future date',
            code: 'EXPIRATION_DATE_IN_PAST'
          });
          return;
        }
      }

      // Generate the API key
      const result = await this.generateApiKeyUseCase.execute({
        clientId: clientId.trim(),
        expiresAt: parsedExpiresAt,
      });

      // Format response (only show key value once)
      const response: ApiKeyGenerationResponse = {
        keyId: result.apiKey.keyId,
        keyValue: result.keyValue, // This is the only time it's shown
        clientId: result.apiKey.clientId,
        createdAt: result.apiKey.createdAt,
        expiresAt: result.apiKey.expiresAt,
      };

      res.status(201).json({
        success: true,
        message: 'API key generated successfully',
        data: response,
        warning: 'Store the keyValue securely - it will not be shown again'
      });

    } catch (error) {
      console.error('API Key generation failed:', error);

      if (error instanceof Error) {
        // Handle known business logic errors
        if (error.message.includes('Client ID is required')) {
          res.status(400).json({
            error: 'Bad Request',
            message: error.message,
            code: 'INVALID_CLIENT_ID'
          });
          return;
        }

        if (error.message.includes('Failed to generate unique key ID')) {
          res.status(500).json({
            error: 'Internal Server Error',
            message: 'Unable to generate unique API key. Please try again.',
            code: 'KEY_GENERATION_FAILED'
          });
          return;
        }
      }

      // Generic error response
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while generating the API key',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  /**
   * Get API keys with pagination and filtering
   * GET /api/v1/api-keys
   */
  async getApiKeys(req: Request, res: Response): Promise<void> {
    try {
      // Parse query parameters
      const page = req.query.page ? parseInt(req.query.page as string, 10) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      const status = req.query.status as string | undefined;
      const clientId = req.query.clientId as string | undefined;

      // Validate query parameters
      if (page < 1) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Page must be a positive integer',
          code: 'INVALID_PAGE'
        });
        return;
      }

      if (limit < 1 || limit > 100) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Limit must be between 1 and 100',
          code: 'INVALID_LIMIT'
        });
        return;
      }

      // Validate status if provided
      if (status && !['ACTIVE', 'REVOKED', 'EXPIRED'].includes(status)) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Status must be one of: ACTIVE, REVOKED, EXPIRED',
          code: 'INVALID_STATUS'
        });
        return;
      }

      // Execute use case
      const result = await this.getApiKeysUseCase.execute({
        page,
        limit,
        status: status as any,
        clientId,
      });

      // Convert to response format
      const responseData = result.data.map(toApiKeyResponseData);

      const response: GetApiKeysResponse = {
        success: true,
        message: result.total === 0 
          ? 'No API keys found' 
          : `Retrieved ${result.data.length} of ${result.total} API keys`,
        data: responseData,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNextPage: result.hasNextPage,
          hasPreviousPage: result.hasPreviousPage,
        },
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('Failed to get API keys:', error);

      if (error instanceof Error) {
        if (error.message.includes('Failed to retrieve API keys')) {
          res.status(500).json({
            error: 'Internal Server Error',
            message: 'Failed to retrieve API keys',
            code: 'RETRIEVAL_FAILED'
          });
          return;
        }
      }

      // Generic error response
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while retrieving API keys',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  /**
   * Health check endpoint for API key service
   * GET /api/v1/api-keys/health
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    res.status(200).json({
      success: true,
      message: 'API Key service is healthy',
      timestamp: new Date().toISOString(),
      service: 'api-key-management'
    });
  }
}
