import { inject, injectable } from 'inversify';
import { <PERSON>pi<PERSON><PERSON> } from '../../../core/entities/api-key.entity';
import { IApiKeyRepository } from '../../../core/ports/api-key.repository';
import { ApiKeyStatus } from '../../../core/types/api-key-status';
import { ApiKeyAdapter } from '../adapters/api-key.adapter';
import { ApiKeyModel } from '../models/api-key.model';

@injectable()
export class MongooseApiKeyRepository implements IApiKeyRepository {
  constructor(
    @inject(ApiKeyAdapter)
    private readonly apiKeyAdapter: ApiKeyAdapter
  ) {}

  /**
   * Create a new API key
   */
  async create(apiKey: {
    keyId: string;
    keyValueHash: string;
    clientId: string;
    status: ApiKeyStatus;
    expiresAt: Date | null;
    lastUsedAt: Date | null;
  }): Promise<ApiKey> {
    try {
      const document = await ApiKeyModel.create({
        keyId: apiKey.keyId,
        keyValueHash: apiKey.keyValueHash,
        clientId: apiKey.clientId,
        status: apiKey.status,
        expiresAt: apiKey.expiresAt,
        lastUsedAt: apiKey.lastUsedAt,
      });

      if (!document) {
        throw new Error('Failed to create API key');
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      if (error instanceof Error && error.message.includes('duplicate key')) {
        throw new Error('API key with this key ID already exists');
      }
      throw error;
    }
  }

  /**
   * Find an API key by its ID
   */
  async findById(id: string): Promise<ApiKey | null> {
    try {
      const document = await ApiKeyModel.findById(id);
      if (!document) {
        return null;
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      return null;
    }
  }

  /**
   * Find an API key by its public key ID
   */
  async findByKeyId(keyId: string): Promise<ApiKey | null> {
    try {
      const document = await ApiKeyModel.findOne({ keyId });
      if (!document) {
        return null;
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      return null;
    }
  }

  /**
   * Find all API keys for a specific client
   */
  async findByClientId(clientId: string): Promise<ApiKey[]> {
    try {
      const documents = await ApiKeyModel.find({ clientId }).sort({ createdAt: -1 });
      return this.apiKeyAdapter.toEntities(documents, ApiKey);
    } catch (error) {
      return [];
    }
  }

  /**
   * Find all API keys with optional pagination and filtering
   */
  async findAll(options?: {
    page?: number;
    limit?: number;
    status?: ApiKeyStatus;
    clientId?: string;
  }): Promise<{ data: ApiKey[]; total: number }> {
    try {
      const { page = 1, limit = 10, status, clientId } = options || {};

      const query: any = {};

      if (status) {
        query.status = status;
      }

      if (clientId) {
        query.clientId = clientId;
      }

      const skip = (page - 1) * limit;

      const [documents, total] = await Promise.all([
        ApiKeyModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }),
        ApiKeyModel.countDocuments(query),
      ]);

      return {
        data: this.apiKeyAdapter.toEntities(documents, ApiKey),
        total,
      };
    } catch (error) {
      return { data: [], total: 0 };
    }
  }

  /**
   * Update an API key
   */
  async update(
    id: string,
    updates: Partial<Omit<ApiKey, 'id' | 'keyId' | 'keyValueHash' | 'createdAt'>>
  ): Promise<ApiKey | null> {
    try {
      const document = await ApiKeyModel.findByIdAndUpdate(
        id,
        { $set: { ...updates, updatedAt: new Date() } },
        { new: true }
      );

      if (!document) {
        return null;
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      return null;
    }
  }

  /**
   * Update the status of an API key
   */
  async updateStatus(id: string, status: ApiKeyStatus): Promise<ApiKey | null> {
    try {
      const document = await ApiKeyModel.findByIdAndUpdate(
        id,
        { $set: { status, updatedAt: new Date() } },
        { new: true }
      );

      if (!document) {
        return null;
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      return null;
    }
  }

  /**
   * Update the last used timestamp for an API key
   */
  async updateLastUsed(id: string): Promise<ApiKey | null> {
    try {
      const now = new Date();
      const document = await ApiKeyModel.findByIdAndUpdate(
        id,
        { $set: { lastUsedAt: now, updatedAt: now } },
        { new: true }
      );

      if (!document) {
        return null;
      }

      return this.apiKeyAdapter.toEntity(document);
    } catch (error) {
      return null;
    }
  }

  /**
   * Delete an API key by ID
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await ApiKeyModel.deleteOne({ _id: id });
      return result.deletedCount > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Delete all API keys for a specific client
   */
  async deleteByClientId(clientId: string): Promise<boolean> {
    try {
      const result = await ApiKeyModel.deleteMany({ clientId });
      return result.deletedCount > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Delete all API keys in the system
   */
  async deleteAll(): Promise<boolean> {
    try {
      const result = await ApiKeyModel.deleteMany({});
      return result.deletedCount > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a key ID already exists
   */
  async existsByKeyId(keyId: string): Promise<boolean> {
    try {
      const count = await ApiKeyModel.countDocuments({ keyId });
      return count > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Find expired API keys that need to be marked as expired
   */
  async findExpiredKeys(): Promise<ApiKey[]> {
    try {
      const now = new Date();
      const documents = await ApiKeyModel.find({
        status: ApiKeyStatus.ACTIVE,
        expiresAt: { $lt: now }
      });

      return this.apiKeyAdapter.toEntities(documents, ApiKey);
    } catch (error) {
      return [];
    }
  }

  /**
   * Bulk update expired keys to EXPIRED status
   */
  async markExpiredKeys(): Promise<number> {
    try {
      const now = new Date();
      const result = await ApiKeyModel.updateMany(
        {
          status: ApiKeyStatus.ACTIVE,
          expiresAt: { $lt: now }
        },
        {
          $set: { status: ApiKeyStatus.EXPIRED, updatedAt: now }
        }
      );

      return result.modifiedCount;
    } catch (error) {
      return 0;
    }
  }
}
