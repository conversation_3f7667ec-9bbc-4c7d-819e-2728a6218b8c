{"name": "settings", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/settings/src", "projectType": "library", "tags": ["scope:settings", "type:lib"], "implicitDependencies": ["shared-utils"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/modules/settings", "main": "modules/settings/src/index.ts", "tsConfig": "modules/settings/tsconfig.lib.json", "assets": ["modules/settings/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["modules/settings/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "modules/settings/jest.config.ts"}}, "test:unit": {"executor": "@nx/jest:jest", "options": {"jestConfig": "modules/settings/jest.config.ts", "testPathPattern": [".*\\.spec\\.ts$"]}}, "test:integration": {"executor": "@nx/jest:jest", "options": {"jestConfig": "modules/settings/jest.config.ts", "testPathPattern": [".*\\.integration\\.spec\\.ts$"]}}, "test:e2e": {"executor": "@nx/jest:jest", "options": {"jestConfig": "modules/settings/jest.config.ts", "testPathPattern": [".*\\.e2e\\.spec\\.ts$"]}}, "test:coverage": {"executor": "@nx/jest:jest", "options": {"jestConfig": "modules/settings/jest.config.ts", "coverage": true, "coverageReporters": ["text", "lcov", "html"]}}, "validate": {"executor": "nx:run-commands", "options": {"command": "ts-node modules/settings/scripts/validate-implementation.ts", "cwd": "{workspaceRoot}"}}, "quick-test": {"executor": "nx:run-commands", "options": {"command": "ts-node modules/settings/scripts/quick-test.ts", "cwd": "{workspaceRoot}"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "ts-node modules/settings/src/server.ts", "cwd": "{workspaceRoot}"}}, "serve:dev": {"executor": "nx:run-commands", "options": {"command": "nodemon --exec ts-node modules/settings/src/server.ts", "cwd": "{workspaceRoot}"}}}}