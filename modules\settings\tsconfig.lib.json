{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": ["node"], "target": "es6", "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@parametry/shared-utils": ["../../dist/modules/shared-utils/src"]}}, "include": ["src/**/*.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}