import { DocumentAdapter } from '@parametry/shared-utils';
import { <PERSON>pi<PERSON><PERSON> } from '../../../core/entities/api-key.entity';
import { ApiKeyDocument } from '../models/api-key.model';
import { injectable, postConstruct } from 'inversify';

@injectable()
export class A<PERSON><PERSON>eyAdapter extends DocumentAdapter<ApiKey, ApiKeyDocument> {
  @postConstruct()
  public override onInit(): void {
    super.onInit();
    // API keys don't have population relationships like agent registration keys
    this.populationMappers = {};
  }

  protected getEntityClass(
    _document: ApiKeyDocument
  ): new (partial: Partial<ApiKey>) => ApiKey {
    return ApiKey;
  }
}
