import { Container } from 'inversify';
import {
  IAgentCommandExecutor,
  infrastructureContainerModule,
  InfrastructureIdentifier,
} from '@parametry/agentix';
import {
  GrpcServerConfig,
  sharedUtilsContainerModule,
  ConnectServer,
  AbstractConnectRouterFactory,
  ConnectServerProvider,
  SharedUtilsIdentifier,
  HttpServerConfig,
} from '@parametry/shared-utils';
import { SystemContainerModule } from '@parametry/systemix';
import { infrastructureContainerModule as SettingsContainerModule } from '@parametry/settings';
import { Application } from '../../app';
import { AgentsController } from '../../core/rest/controllers/agents.controller';
import { ApiKeysController } from '../../core/rest/controllers/api-keys.controller';
import { ExpressApp } from '../express/express-app';
import { BluePrintJobService } from '../jobs';
import { AppTypes, ControllerTypes } from './identifiers';
import { Health<PERSON>ontroller } from '../../core/rest/controllers/health.controller';
import { UnitController } from '../../core/rest/controllers/unit.controller';
import { ProtocolsController } from '../../core/rest/controllers/protocols.controller';
import { DevicesController } from '../../core/rest/controllers/devices.controller';
import { WasmContainerModule } from '@parametry/wasmix';
import { MonitoringPointController } from '../../core/rest/controllers/monitoring-point.controller';
import { MonitoringSourceController } from '../../core/rest/controllers/monitoring-source.controller';

import { ConnectAgentService } from '../../core/connect/services/connect-agent-service';
import { ConnectRouterFactory } from '../connect/server/router-factory';
import { CommandChannelService } from '../../core/connect/services/command-channel.service';
import { CommandManager } from '../../core/connect/services/command-manager.service';
import { ICommandManager } from '../../core/connect/interfaces';
import { BluePrintGrpcServerConfig, BluePrintHttpServerConfig } from '../../config';

const container = new Container();

// Modules
container.load(
  infrastructureContainerModule,
  sharedUtilsContainerModule,
  SystemContainerModule,
  WasmContainerModule,
  SettingsContainerModule
);

// Applications
container.bind<Application>(AppTypes.Application).to(Application).inSingletonScope();
container.bind<ExpressApp>(SharedUtilsIdentifier.ExpressApp).to(ExpressApp).inSingletonScope();
container
  .bind<BluePrintJobService>(AppTypes.BluePrintJobService)
  .to(BluePrintJobService)
  .inSingletonScope();
// Connect-RPC Bindings
container
  .bind<ConnectAgentService>(AppTypes.ConnectAgentService)
  .to(ConnectAgentService)
  .inSingletonScope();
container.bind<ConnectServer>(AppTypes.ConnectServer).to(ConnectServer).inSingletonScope();
container
  .bind<ConnectServerProvider>(AppTypes.ConnectServerProvider)
  .to(ConnectServerProvider)
  .inSingletonScope();
container
  .bind<AbstractConnectRouterFactory>(AbstractConnectRouterFactory)
  .to(ConnectRouterFactory)
  .inSingletonScope();

// Command Management System
container
  .bind<CommandChannelService>(AppTypes.CommandChannelManager)
  .to(CommandChannelService)
  .inSingletonScope();
container.bind<ICommandManager>(AppTypes.CommandManager).to(CommandManager).inSingletonScope();

// Controllers
container
  .bind<AgentsController>(ControllerTypes.AgentsController)
  .to(AgentsController)
  .inSingletonScope();
container
  .bind<HealthController>(ControllerTypes.HealthController)
  .to(HealthController)
  .inSingletonScope();
container
  .bind<UnitController>(ControllerTypes.UnitController)
  .to(UnitController)
  .inSingletonScope();
container
  .bind<ProtocolsController>(ControllerTypes.ProtocolsController)
  .to(ProtocolsController)
  .inSingletonScope();
container
  .bind<DevicesController>(ControllerTypes.DevicesController)
  .to(DevicesController)
  .inSingletonScope();
container
  .bind<MonitoringPointController>(ControllerTypes.MonitoringPointsController)
  .to(MonitoringPointController)
  .inSingletonScope();
container
  .bind<MonitoringSourceController>(ControllerTypes.MonitoringSourcesController)
  .to(MonitoringSourceController)
  .inSingletonScope();
container
  .bind<ApiKeysController>(ControllerTypes.ApiKeysController)
  .to(ApiKeysController)
  .inSingletonScope();

container
  .bind<IAgentCommandExecutor>(InfrastructureIdentifier.AgentCommandExecutor)
  .to(CommandManager) // Direct binding to the new manager
  .inSingletonScope();

container
  .bind<GrpcServerConfig>(AppTypes.GrpcServerConfig)
  .to(BluePrintGrpcServerConfig)
  .inSingletonScope();

container
  .bind<HttpServerConfig>(SharedUtilsIdentifier.HttpServerConfig)
  .to(BluePrintHttpServerConfig)
  .inSingletonScope();

container.bind<Container>(SharedUtilsIdentifier.GlobalContainer).toConstantValue(container);
export default container;
